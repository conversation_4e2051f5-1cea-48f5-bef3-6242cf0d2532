# Kui TUI 开发计划

## 项目概述
构建一个基于 Terminal 的 Kubernetes 资源管理 TUI 应用，结合 k9s 和 lens 的优点。

## 技术栈
- Go 语言
- Cobra (CLI框架)
- Bubbletea (TUI引擎)
- client-go (K8s客户端)

## 分层架构
```
CLI层 (Cobra) → TUI层 (Bubbletea) → K8s客户端层 (client-go) → 数据模型层
```

## 执行步骤
1. **项目基础结构搭建** - 目录结构、依赖管理
2. **CLI框架实现** - 根命令、版本命令、配置管理
3. **K8s客户端集成** - 客户端封装、资源获取接口
4. **TUI基础框架** - Bubbletea应用、基础布局
5. **资源浏览功能** - 资源列表、详情查看、交互功能

## 目标用户
DevOps 与运维人员

## 核心功能优先级
资源浏览 > 监控 > 日志查看 > 编辑

## 部署方式
单一可执行文件

## 优化任务 (2025-06-05)
1. **精简命令结构**:
   - `./kui` 直接启动 TUI
   - `./kui --help/-h/help` 显示帮助
   - `./kui version/-v/--version` 显示版本
2. **修复退出问题**: 确保按 `q` 能正常退出
3. **优化UI界面**: 参考 lazygit 风格设计

## 完成状态 (2025-06-06)
✅ **任务1 - 精简命令结构**: 完成
- 根命令默认启动TUI
- 支持多种版本查看方式
- 错误命令正确显示帮助

✅ **任务2 - 修复退出问题**: 完成
- 在ResourceListModel中添加了q和ctrl+c退出处理
- 测试确认退出功能正常

✅ **任务3 - 优化UI界面**: 完成
- 参考lazygit设计了新的配色方案
- 绿色标题栏，蓝色选中状态
- 状态颜色编码（绿色Running，红色Failed等）
- 改进的状态栏布局和快捷键提示
- 清晰的分隔线和面板布局

## 最终功能验证
✅ 命令行接口：所有命令变体正常工作
✅ TUI界面：美观的lazygit风格界面
✅ 资源浏览：支持5种资源类型切换
✅ 键盘导航：数字键切换，q键退出
✅ 实时数据：连接K8s集群显示真实资源
✅ 错误处理：优雅的错误提示和降级体验

## UI重构任务 (2025-06-06)
基于 ui.txt 设计规范重构界面：
1. **创建样式系统**: 统一的颜色、间距、边框样式
2. **重构布局组件**: 四栏布局（header、sidebar、content、footer）
3. **重新设计组件**: Header、Sidebar、Content、Footer
4. **国际化改进**: 界面文本英文化，添加icons
5. **集成K8s信息**: Header显示cluster context和namespace

## UI重构完成状态 (2025-06-06)
✅ **样式系统**: 创建了完整的styles包，统一配色和组件样式
✅ **四栏布局**: 使用lipgloss实现Header、Sidebar、Content、Footer分离
✅ **Header组件**: 显示应用名称和集群信息（cluster: orbstack namespace: default）
✅ **Sidebar组件**: 垂直资源导航，带图标和选中状态（▶ 🟢 Pod）
✅ **Content组件**: 资源列表显示，支持加载状态和错误处理
✅ **Footer组件**: 快捷键提示和状态信息（Resources: 1 | Updated: 00:02:44）
✅ **英文界面**: 所有文本英文化，保留中文应用名"葵 (kui)"
✅ **图标系统**: 资源类型图标（🟢🔵🚀🖥️📁）和状态图标（●◐✗✓）
✅ **功能验证**: 资源切换、退出功能、实时数据更新全部正常
