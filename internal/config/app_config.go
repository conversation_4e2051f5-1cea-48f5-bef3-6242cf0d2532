package config

import (
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
	"k8s.io/client-go/util/homedir"
)

// AppConfig 应用配置结构
type AppConfig struct {
	Theme       ThemeConfig       `yaml:"theme"`
	Keybindings KeybindingsConfig `yaml:"keybindings"`
	Layout      LayoutConfig      `yaml:"layout"`
	General     GeneralConfig     `yaml:"general"`
}

// ThemeConfig 主题配置
type ThemeConfig struct {
	ColorScheme string `yaml:"color_scheme"` // catppuccin-mocha, catppuccin-latte, etc.
	Variant     string `yaml:"variant"`      // mocha, latte, frappe, macchiato
}

// KeybindingsConfig 快捷键配置
type KeybindingsConfig struct {
	Quit         []string `yaml:"quit"`
	Refresh      []string `yaml:"refresh"`
	Navigate     []string `yaml:"navigate"`
	SelectPod    string   `yaml:"select_pod"`
	SelectSvc    string   `yaml:"select_service"`
	SelectDeploy string   `yaml:"select_deployment"`
	SelectNode   string   `yaml:"select_node"`
	SelectNS     string   `yaml:"select_namespace"`
}

// LayoutConfig 布局配置
type LayoutConfig struct {
	SidebarWidth    int  `yaml:"sidebar_width"`
	ShowIcons       bool `yaml:"show_icons"`
	ShowStatusIcons bool `yaml:"show_status_icons"`
	CompactMode     bool `yaml:"compact_mode"`
}

// GeneralConfig 通用配置
type GeneralConfig struct {
	RefreshInterval int    `yaml:"refresh_interval"` // 秒
	Language        string `yaml:"language"`         // en, zh
	LogLevel        string `yaml:"log_level"`        // debug, info, warn, error
}

// DefaultAppConfig 返回默认应用配置
func DefaultAppConfig() *AppConfig {
	return &AppConfig{
		Theme: ThemeConfig{
			ColorScheme: "catppuccin",
			Variant:     "mocha",
		},
		Keybindings: KeybindingsConfig{
			Quit:         []string{"q", "ctrl+c"},
			Refresh:      []string{"r", "f5"},
			Navigate:     []string{"j", "k", "up", "down"},
			SelectPod:    "1",
			SelectSvc:    "2",
			SelectDeploy: "3",
			SelectNode:   "4",
			SelectNS:     "5",
		},
		Layout: LayoutConfig{
			SidebarWidth:    20,
			ShowIcons:       true,
			ShowStatusIcons: true,
			CompactMode:     false,
		},
		General: GeneralConfig{
			RefreshInterval: 5,
			Language:        "en",
			LogLevel:        "info",
		},
	}
}

// LoadAppConfig 加载应用配置
func LoadAppConfig(configPath string) (*AppConfig, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = getDefaultAppConfigPath()
	}

	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		defaultConfig := DefaultAppConfig()
		if err := SaveAppConfig(defaultConfig, configPath); err != nil {
			return nil, err
		}
		return defaultConfig, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	// 解析 YAML
	var config AppConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	// 验证配置并填充默认值
	validateAndFillDefaults(&config)

	return &config, nil
}

// SaveAppConfig 保存应用配置
func SaveAppConfig(config *AppConfig, configPath string) error {
	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}

	// 序列化为 YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(configPath, data, 0644)
}

// getDefaultAppConfigPath 获取默认应用配置文件路径
func getDefaultAppConfigPath() string {
	if home := homedir.HomeDir(); home != "" {
		return filepath.Join(home, ".kui.yaml")
	}
	return ".kui.yaml"
}

// validateAndFillDefaults 验证配置并填充默认值
func validateAndFillDefaults(config *AppConfig) {
	defaultConfig := DefaultAppConfig()

	// 验证主题配置
	if config.Theme.ColorScheme == "" {
		config.Theme.ColorScheme = defaultConfig.Theme.ColorScheme
	}
	if config.Theme.Variant == "" {
		config.Theme.Variant = defaultConfig.Theme.Variant
	}

	// 验证快捷键配置
	if len(config.Keybindings.Quit) == 0 {
		config.Keybindings.Quit = defaultConfig.Keybindings.Quit
	}
	if len(config.Keybindings.Refresh) == 0 {
		config.Keybindings.Refresh = defaultConfig.Keybindings.Refresh
	}
	if len(config.Keybindings.Navigate) == 0 {
		config.Keybindings.Navigate = defaultConfig.Keybindings.Navigate
	}
	if config.Keybindings.SelectPod == "" {
		config.Keybindings.SelectPod = defaultConfig.Keybindings.SelectPod
	}
	if config.Keybindings.SelectSvc == "" {
		config.Keybindings.SelectSvc = defaultConfig.Keybindings.SelectSvc
	}
	if config.Keybindings.SelectDeploy == "" {
		config.Keybindings.SelectDeploy = defaultConfig.Keybindings.SelectDeploy
	}
	if config.Keybindings.SelectNode == "" {
		config.Keybindings.SelectNode = defaultConfig.Keybindings.SelectNode
	}
	if config.Keybindings.SelectNS == "" {
		config.Keybindings.SelectNS = defaultConfig.Keybindings.SelectNS
	}

	// 验证布局配置
	if config.Layout.SidebarWidth <= 0 {
		config.Layout.SidebarWidth = defaultConfig.Layout.SidebarWidth
	}

	// 验证通用配置
	if config.General.RefreshInterval <= 0 {
		config.General.RefreshInterval = defaultConfig.General.RefreshInterval
	}
	if config.General.Language == "" {
		config.General.Language = defaultConfig.General.Language
	}
	if config.General.LogLevel == "" {
		config.General.LogLevel = defaultConfig.General.LogLevel
	}
}

// GetConfigPath 获取配置文件路径
func GetConfigPath(customPath string) string {
	if customPath != "" {
		return customPath
	}
	return getDefaultAppConfigPath()
}
