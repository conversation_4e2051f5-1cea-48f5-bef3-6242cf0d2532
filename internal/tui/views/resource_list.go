package views

import (
	"context"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/config"
	"github.com/liyujun-dev/kui/internal/k8s"
	"github.com/liyujun-dev/kui/internal/models"
	"github.com/liyujun-dev/kui/internal/tui/styles"
)

// ResourceListModel 资源列表模型
type ResourceListModel struct {
	width        int
	height       int
	client       *k8s.Client
	config       *config.Config
	appConfig    *config.AppConfig
	resourceType models.ResourceType
	namespace    string
	clusterName  string
	pods         []*models.PodResource
	services     []*models.ServiceResource
	deployments  []*models.DeploymentResource
	nodes        []*models.NodeResource
	namespaces   []*models.Resource
	cursor       int
	loading      bool
	err          error
	lastUpdate   time.Time
}

// NewResourceListModel 创建新的资源列表模型
func NewResourceListModel() (*ResourceListModel, error) {
	return NewResourceListModelWithConfig(config.DefaultAppConfig())
}

// NewResourceListModelWithConfig 使用指定配置创建资源列表模型
func NewResourceListModelWithConfig(appConfig *config.AppConfig) (*ResourceListModel, error) {
	cfg := config.DefaultConfig()
	client, err := k8s.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建 K8s 客户端失败: %w", err)
	}

	// 获取集群信息
	clusterName, namespace, _ := cfg.GetClusterInfo()
	if namespace == "" {
		namespace = "default"
	}
	if clusterName == "" {
		clusterName = "unknown"
	}

	return &ResourceListModel{
		client:       client,
		config:       cfg,
		appConfig:    appConfig,
		resourceType: models.ResourceTypePod,
		namespace:    namespace,
		clusterName:  clusterName,
		loading:      true,
	}, nil
}

// Init 初始化
func (m *ResourceListModel) Init() tea.Cmd {
	return m.refreshResources()
}

// Update 更新模型
func (m *ResourceListModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			maxCursor := m.getMaxCursor()
			if m.cursor < maxCursor {
				m.cursor++
			}
		case "1":
			m.resourceType = models.ResourceTypePod
			m.cursor = 0
			return m, m.refreshResources()
		case "2":
			m.resourceType = models.ResourceTypeService
			m.cursor = 0
			return m, m.refreshResources()
		case "3":
			m.resourceType = models.ResourceTypeDeployment
			m.cursor = 0
			return m, m.refreshResources()
		case "4":
			m.resourceType = models.ResourceTypeNode
			m.cursor = 0
			return m, m.refreshResources()
		case "5":
			m.resourceType = models.ResourceTypeNamespace
			m.cursor = 0
			return m, m.refreshResources()
		case "r":
			m.loading = true
			return m, m.refreshResources()
		}

	case refreshMsg:
		m.loading = false
		m.lastUpdate = time.Now()
		if msg.err != nil {
			m.err = msg.err
		} else {
			m.err = nil
			m.pods = msg.pods
			m.services = msg.services
			m.deployments = msg.deployments
			m.nodes = msg.nodes
			m.namespaces = msg.namespaces
		}
		return m, nil
	}

	return m, nil
}

// View 渲染视图
func (m *ResourceListModel) View() string {
	if m.width == 0 || m.height == 0 {
		return "Initializing..."
	}

	// Calculate layout dimensions
	sidebarWidth := 20
	contentWidth := m.width - sidebarWidth - 1 // -1 for border
	if contentWidth < 40 {
		contentWidth = 40
	}

	headerHeight := 3
	footerHeight := 3
	bodyHeight := m.height - headerHeight - footerHeight

	// Render components
	header := m.renderHeader()
	sidebar := m.renderSidebar(sidebarWidth, bodyHeight)
	content := m.renderContent(contentWidth, bodyHeight)
	footer := m.renderFooter()

	// Combine sidebar and content horizontally
	body := lipgloss.JoinHorizontal(
		lipgloss.Top,
		sidebar,
		content,
	)

	// Combine all components vertically
	return lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		body,
		footer,
	)
}

// renderHeader 渲染头部
func (m *ResourceListModel) renderHeader() string {
	headerStyle := styles.HeaderStyle.
		Width(m.width)

	// Left side: Application info
	leftPart := "葵 (kui)"

	// Right side: Cluster info
	rightPart := fmt.Sprintf("cluster: %s  namespace: %s", m.clusterName, m.namespace)

	// Calculate padding
	contentLen := len(leftPart) + len(rightPart)
	padding := ""
	if m.width > contentLen+4 { // +4 for padding
		padding = strings.Repeat(" ", m.width-contentLen-4)
	}

	content := leftPart + padding + rightPart
	return headerStyle.Render(content)
}

// renderSidebar 渲染侧边栏
func (m *ResourceListModel) renderSidebar(width, height int) string {
	sidebarStyle := styles.SidebarStyle.
		Width(width).
		Height(height)

	items := []struct {
		key  string
		name string
		icon string
		rt   models.ResourceType
	}{
		{"1", "Pod", "🟢", models.ResourceTypePod},
		{"2", "Service", "🔵", models.ResourceTypeService},
		{"3", "Deployment", "🚀", models.ResourceTypeDeployment},
		{"4", "Node", "🖥️", models.ResourceTypeNode},
		{"5", "Namespace", "📁", models.ResourceTypeNamespace},
	}

	var navItems []string
	for _, item := range items {
		var line string
		if item.rt == m.resourceType {
			// Selected state
			style := styles.SidebarSelectedStyle.
				Width(width - 4) // Account for padding and border
			line = style.Render(fmt.Sprintf("▶ %s %s", item.icon, item.name))
		} else {
			// Unselected state
			style := styles.SidebarItemStyle.
				Width(width - 4)
			line = style.Render(fmt.Sprintf("  %s %s", item.icon, item.name))
		}
		navItems = append(navItems, line)
	}

	// Fill remaining space
	for len(navItems) < height-2 { // -2 for padding
		navItems = append(navItems, "")
	}

	content := strings.Join(navItems, "\n")
	return sidebarStyle.Render(content)
}

// renderContent 渲染内容区域
func (m *ResourceListModel) renderContent(width, height int) string {
	contentStyle := styles.ContentStyle.
		Width(width).
		Height(height)

	var content string
	if m.loading {
		content = styles.LoadingStyle.Render("Loading resources...")
	} else if m.err != nil {
		content = styles.ErrorStyle.Render(fmt.Sprintf("Error: %v", m.err))
	} else {
		content = m.renderResourceList()
	}

	return contentStyle.Render(content)
}

// renderResourceList 渲染资源列表
func (m *ResourceListModel) renderResourceList() string {
	switch m.resourceType {
	case models.ResourceTypePod:
		return m.renderPodList()
	case models.ResourceTypeService:
		return m.renderServiceList()
	case models.ResourceTypeDeployment:
		return m.renderDeploymentList()
	case models.ResourceTypeNode:
		return m.renderNodeList()
	case models.ResourceTypeNamespace:
		return m.renderNamespaceList()
	default:
		return "Unknown resource type"
	}
}

// renderPodList 渲染 Pod 列表
func (m *ResourceListModel) renderPodList() string {
	if len(m.pods) == 0 {
		return styles.EmptyStyle.Render("No Pod resources found")
	}

	var lines []string

	// Header style
	headerStyle := styles.ContentHeaderStyle

	header := fmt.Sprintf("%-30s %-8s %-12s %-8s %-8s", "NAME", "READY", "STATUS", "RESTARTS", "AGE")
	lines = append(lines, headerStyle.Render(header))

	for i, pod := range m.pods {
		var style lipgloss.Style
		if i == m.cursor {
			// Selected row style
			style = styles.ContentSelectedRowStyle
		} else {
			// Normal row style
			style = styles.ContentRowStyle
		}

		// Status color
		statusColor := styles.GetStatusColor(string(pod.Status))
		statusStyle := lipgloss.NewStyle().Foreground(statusColor)
		statusIcon := styles.GetStatusIcon(string(pod.Status))

		line := fmt.Sprintf("%-30s %-8s %s %-11s %-8d %-8s",
			truncate(pod.Name, 30),
			pod.Ready,
			statusIcon,
			statusStyle.Render(string(pod.Status)),
			pod.Restarts,
			pod.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderServiceList 渲染 Service 列表
func (m *ResourceListModel) renderServiceList() string {
	if len(m.services) == 0 {
		return styles.EmptyStyle.Render("No Service resources found")
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-15s %-15s %-20s %-10s", "NAME", "TYPE", "CLUSTER-IP", "EXTERNAL-IP", "AGE")
	lines = append(lines, styles.ContentHeaderStyle.Render(header))

	for i, service := range m.services {
		var style lipgloss.Style
		if i == m.cursor {
			style = styles.ContentSelectedRowStyle
		} else {
			style = styles.ContentRowStyle
		}

		line := fmt.Sprintf("%-30s %-15s %-15s %-20s %-10s",
			truncate(service.Name, 30),
			service.Type,
			service.ClusterIP,
			truncate(service.ExternalIP, 20),
			service.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderDeploymentList 渲染 Deployment 列表
func (m *ResourceListModel) renderDeploymentList() string {
	if len(m.deployments) == 0 {
		return styles.EmptyStyle.Render("No Deployment resources found")
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-10s %-10s %-10s %-10s", "NAME", "READY", "UP-TO-DATE", "AVAILABLE", "AGE")
	lines = append(lines, styles.ContentHeaderStyle.Render(header))

	for i, deployment := range m.deployments {
		var style lipgloss.Style
		if i == m.cursor {
			style = styles.ContentSelectedRowStyle
		} else {
			style = styles.ContentRowStyle
		}

		line := fmt.Sprintf("%-30s %-10s %-10d %-10d %-10s",
			truncate(deployment.Name, 30),
			deployment.Ready,
			deployment.UpToDate,
			deployment.Available,
			deployment.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderNodeList 渲染 Node 列表
func (m *ResourceListModel) renderNodeList() string {
	if len(m.nodes) == 0 {
		return styles.EmptyStyle.Render("No Node resources found")
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-10s %-15s %-15s %-10s", "NAME", "STATUS", "ROLES", "VERSION", "AGE")
	lines = append(lines, styles.ContentHeaderStyle.Render(header))

	for i, node := range m.nodes {
		var style lipgloss.Style
		if i == m.cursor {
			style = styles.ContentSelectedRowStyle
		} else {
			style = styles.ContentRowStyle
		}

		status := "Ready"
		if !node.Ready {
			status = "NotReady"
		}

		roles := strings.Join(node.Roles, ",")
		if roles == "" {
			roles = "<none>"
		}

		line := fmt.Sprintf("%-30s %-10s %-15s %-15s %-10s",
			truncate(node.Name, 30),
			status,
			truncate(roles, 15),
			truncate(node.Version, 15),
			node.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderNamespaceList 渲染 Namespace 列表
func (m *ResourceListModel) renderNamespaceList() string {
	if len(m.namespaces) == 0 {
		return styles.EmptyStyle.Render("No Namespace resources found")
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-15s %-10s", "NAME", "STATUS", "AGE")
	lines = append(lines, styles.ContentHeaderStyle.Render(header))

	for i, namespace := range m.namespaces {
		var style lipgloss.Style
		if i == m.cursor {
			style = styles.ContentSelectedRowStyle
		} else {
			style = styles.ContentRowStyle
		}

		line := fmt.Sprintf("%-30s %-15s %-10s",
			truncate(namespace.Name, 30),
			string(namespace.Status),
			namespace.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderFooter 渲染底部状态栏
func (m *ResourceListModel) renderFooter() string {
	footerStyle := styles.FooterStyle.
		Width(m.width)

	// Shortcut hints
	keyStyle := styles.FooterKeyStyle

	shortcuts := []string{
		fmt.Sprintf("%s: Select", keyStyle.Render("tab")),
		fmt.Sprintf("%s: Quit", keyStyle.Render("q")),
		fmt.Sprintf("%s: Refresh", keyStyle.Render("r")),
	}

	// Status info
	var statusInfo string
	if !m.lastUpdate.IsZero() {
		count := m.getResourceCount()
		countStyle := lipgloss.NewStyle().Foreground(styles.Success)
		timeStyle := lipgloss.NewStyle().Foreground(styles.TextDim)

		statusInfo = fmt.Sprintf("Resources: %s | Updated: %s",
			countStyle.Render(fmt.Sprintf("%d", count)),
			timeStyle.Render(m.lastUpdate.Format("15:04:05")))
	}

	// Combine shortcuts and status
	leftPart := strings.Join(shortcuts, "  ")
	rightPart := statusInfo

	// Calculate padding
	contentLen := lipgloss.Width(leftPart) + lipgloss.Width(rightPart)
	padding := ""
	if m.width > contentLen+4 { // +4 for padding
		padding = strings.Repeat(" ", m.width-contentLen-4)
	}

	content := leftPart + padding + rightPart
	return footerStyle.Render(content)
}

// getMaxCursor 获取最大光标位置
func (m *ResourceListModel) getMaxCursor() int {
	count := m.getResourceCount()
	if count > 0 {
		return count - 1
	}
	return 0
}

// getResourceCount 获取当前资源数量
func (m *ResourceListModel) getResourceCount() int {
	switch m.resourceType {
	case models.ResourceTypePod:
		return len(m.pods)
	case models.ResourceTypeService:
		return len(m.services)
	case models.ResourceTypeDeployment:
		return len(m.deployments)
	case models.ResourceTypeNode:
		return len(m.nodes)
	case models.ResourceTypeNamespace:
		return len(m.namespaces)
	default:
		return 0
	}
}

// refreshMsg 刷新消息
type refreshMsg struct {
	pods        []*models.PodResource
	services    []*models.ServiceResource
	deployments []*models.DeploymentResource
	nodes       []*models.NodeResource
	namespaces  []*models.Resource
	err         error
}

// refreshResources 刷新资源
func (m *ResourceListModel) refreshResources() tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()

		var pods []*models.PodResource
		var services []*models.ServiceResource
		var deployments []*models.DeploymentResource
		var nodes []*models.NodeResource
		var namespaces []*models.Resource
		var err error

		switch m.resourceType {
		case models.ResourceTypePod:
			pods, err = m.client.ListPods(ctx, m.namespace)
		case models.ResourceTypeService:
			services, err = m.client.ListServices(ctx, m.namespace)
		case models.ResourceTypeDeployment:
			deployments, err = m.client.ListDeployments(ctx, m.namespace)
		case models.ResourceTypeNode:
			nodes, err = m.client.ListNodes(ctx)
		case models.ResourceTypeNamespace:
			namespaces, err = m.client.ListNamespaces(ctx)
		}

		return refreshMsg{
			pods:        pods,
			services:    services,
			deployments: deployments,
			nodes:       nodes,
			namespaces:  namespaces,
			err:         err,
		}
	}
}

// truncate 截断字符串
func truncate(s string, length int) string {
	if len(s) <= length {
		return s
	}
	return s[:length-3] + "..."
}
