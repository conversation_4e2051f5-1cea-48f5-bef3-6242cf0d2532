package styles

import "github.com/charmbracelet/lipgloss"

// Catppuccin Mocha color palette - soothing pastel theme
var (
	// Primary colors - Catppuccin Mocha
	Primary   = lipgloss.Color("#cba6f7") // Mauve
	Secondary = lipgloss.Color("#89b4fa") // Blue
	Success   = lipgloss.Color("#a6e3a1") // Green
	Warning   = lipgloss.Color("#f9e2af") // Yellow
	Error     = lipgloss.Color("#f38ba8") // Red
	Info      = lipgloss.Color("#89dceb") // Sky

	// Neutral colors - Catppuccin Mocha
	Background = lipgloss.Color("#1e1e2e") // Base
	Surface    = lipgloss.Color("#313244") // Surface0
	Border     = lipgloss.Color("#585b70") // Surface2
	Text       = lipgloss.Color("#cdd6f4") // Text
	TextMuted  = lipgloss.Color("#a6adc8") // Subtext0
	TextDim    = lipgloss.Color("#9399b2") // Overlay2

	// Status colors
	StatusRunning     = Success
	StatusPending     = Warning
	StatusFailed      = Error
	StatusTerminating = lipgloss.Color("#fab387") // Peach
	StatusUnknown     = TextMuted

	// Selection colors
	Selected    = lipgloss.Color("#45475a") // Surface1
	Highlighted = lipgloss.Color("#585b70") // Surface2
)

// Common styles
var (
	// Base styles
	BaseStyle = lipgloss.NewStyle().
			Foreground(Text).
			Background(Background)

	// Header styles
	HeaderStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(Text).
			Background(Surface).
			Padding(0, 1).
			Border(lipgloss.NormalBorder(), false, false, true, false).
			BorderForeground(Border)

	// Sidebar styles
	SidebarStyle = lipgloss.NewStyle().
			Background(Surface).
			Border(lipgloss.NormalBorder(), false, true, false, false).
			BorderForeground(Border).
			Padding(1, 1)

	SidebarItemStyle = lipgloss.NewStyle().
				Foreground(TextMuted).
				Padding(0, 1)

	SidebarSelectedStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(Text).
				Background(Selected).
				Padding(0, 1)

	// Content styles
	ContentStyle = lipgloss.NewStyle().
			Background(Background).
			Padding(1, 1)

	ContentHeaderStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(Text).
				Background(Surface).
				Padding(0, 1)

	ContentRowStyle = lipgloss.NewStyle().
			Foreground(Text).
			Padding(0, 1)

	ContentSelectedRowStyle = lipgloss.NewStyle().
				Bold(true).
				Foreground(Text).
				Background(Selected).
				Padding(0, 1)

	// Footer styles
	FooterStyle = lipgloss.NewStyle().
			Foreground(TextMuted).
			Background(Surface).
			Border(lipgloss.NormalBorder(), true, false, false, false).
			BorderForeground(Border).
			Padding(0, 1)

	FooterKeyStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(Primary)

	// Status styles
	StatusStyle = lipgloss.NewStyle().
			Bold(true)

	// Empty state styles
	EmptyStyle = lipgloss.NewStyle().
			Foreground(TextMuted).
			Italic(true).
			Padding(2, 0)

	// Error styles
	ErrorStyle = lipgloss.NewStyle().
			Foreground(Error).
			Bold(true)

	// Loading styles
	LoadingStyle = lipgloss.NewStyle().
			Foreground(Info).
			Italic(true)
)

// GetStatusColor returns the appropriate color for a given status
func GetStatusColor(status string) lipgloss.Color {
	switch status {
	case "Running", "Ready", "Active":
		return StatusRunning
	case "Pending", "Waiting":
		return StatusPending
	case "Failed", "Error", "CrashLoopBackOff":
		return StatusFailed
	case "Terminating", "Stopping":
		return StatusTerminating
	case "Succeeded", "Completed":
		return Success
	default:
		return StatusUnknown
	}
}

// GetStatusStyle returns a styled status string
func GetStatusStyle(status string) lipgloss.Style {
	return StatusStyle.Foreground(GetStatusColor(status))
}

// Icons for different resource types and states
const (
	IconPod        = "🟢"
	IconService    = "🔵"
	IconDeployment = "🚀"
	IconNode       = "🖥️"
	IconNamespace  = "📁"
	IconSelected   = "▶"
	IconUnselected = " "
	IconRunning    = "●"
	IconPending    = "◐"
	IconFailed     = "✗"
	IconSuccess    = "✓"
	IconWarning    = "⚠"
	IconInfo       = "ℹ"
)

// GetResourceIcon returns the appropriate icon for a resource type
func GetResourceIcon(resourceType string) string {
	switch resourceType {
	case "Pod":
		return IconPod
	case "Service":
		return IconService
	case "Deployment":
		return IconDeployment
	case "Node":
		return IconNode
	case "Namespace":
		return IconNamespace
	default:
		return "📄"
	}
}

// GetStatusIcon returns the appropriate icon for a status
func GetStatusIcon(status string) string {
	switch status {
	case "Running", "Ready", "Active":
		return IconRunning
	case "Pending", "Waiting":
		return IconPending
	case "Failed", "Error", "CrashLoopBackOff":
		return IconFailed
	case "Succeeded", "Completed":
		return IconSuccess
	default:
		return IconInfo
	}
}

// Layout constants
const (
	SidebarWidth    = 20
	MinContentWidth = 40
	HeaderHeight    = 3
	FooterHeight    = 3
)

// Helper functions for layout calculations
func CalculateContentWidth(totalWidth int) int {
	contentWidth := totalWidth - SidebarWidth - 2 // -2 for borders
	if contentWidth < MinContentWidth {
		return MinContentWidth
	}
	return contentWidth
}

func CalculateContentHeight(totalHeight int) int {
	return totalHeight - HeaderHeight - FooterHeight
}
